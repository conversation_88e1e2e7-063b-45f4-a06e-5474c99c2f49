/**
 * @file emergency-fix.js - AI侧边栏紧急修复脚本
 * @description 修复侧边栏按钮失效和初始化问题
 */

console.log('🚨 开始紧急修复AI侧边栏...');

/**
 * @function emergencyButtonFix - 紧急修复按钮功能
 */
function emergencyButtonFix() {
    console.log('🔧 修复按钮事件监听器...');
    
    // 基本按钮处理函数
    const buttonHandlers = {
        'aisp-settings-btn': () => {
            console.log('设置按钮被点击');
            if (chrome.tabs) {
                chrome.tabs.create({
                    url: chrome.runtime.getURL('src/settings/settings.html')
                });
            }
        },
        
        'aisp-refresh-btn': () => {
            console.log('刷新按钮被点击');
            location.reload();
        },
        
        'aisp-debug-btn': () => {
            console.log('调试按钮被点击');
            if (typeof aisp_debugConnectionStatus === 'function') {
                aisp_debugConnectionStatus();
            } else {
                console.log('调试功能未加载');
            }
        },
        
        'aisp-send-btn': () => {
            console.log('发送按钮被点击');
            const input = document.getElementById('aisp-user-input');
            if (input && input.value.trim()) {
                console.log('发送消息:', input.value);
                // 简化的发送逻辑
                emergencySendMessage(input.value);
                input.value = '';
            }
        },
        
        'aisp-clear-btn': () => {
            console.log('清空按钮被点击');
            const input = document.getElementById('aisp-user-input');
            if (input) {
                input.value = '';
                input.focus();
            }
        },
        
        'aisp-test-connection-btn': () => {
            console.log('测试连接按钮被点击');
            emergencyTestConnection();
        },
        
        'aisp-template-manager-btn': () => {
            console.log('模板管理按钮被点击');
            alert('模板管理功能正在修复中...');
        },
        
        'aisp-knowledge-base-btn': () => {
            console.log('知识库按钮被点击');
            alert('知识库功能正在修复中...');
        }
    };
    
    // 为每个按钮添加事件监听器
    Object.entries(buttonHandlers).forEach(([buttonId, handler]) => {
        const button = document.getElementById(buttonId);
        if (button) {
            // 移除现有监听器
            button.replaceWith(button.cloneNode(true));
            const newButton = document.getElementById(buttonId);
            
            // 添加新的监听器
            newButton.addEventListener('click', (event) => {
                event.preventDefault();
                event.stopPropagation();
                try {
                    handler();
                } catch (error) {
                    console.error(`按钮 ${buttonId} 处理失败:`, error);
                }
            });
            
            // 确保按钮可点击
            newButton.style.pointerEvents = 'auto';
            newButton.disabled = false;
            
            console.log(`✅ ${buttonId} 事件监听器已修复`);
        } else {
            console.warn(`❌ 按钮 ${buttonId} 未找到`);
        }
    });
    
    // 修复输入框回车事件
    const userInput = document.getElementById('aisp-user-input');
    if (userInput) {
        userInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                buttonHandlers['aisp-send-btn']();
            }
        });
        console.log('✅ 输入框回车事件已修复');
    }
}

/**
 * @function emergencySendMessage - 紧急发送消息功能
 * @param {string} message - 要发送的消息
 */
async function emergencySendMessage(message) {
    try {
        console.log('📤 发送消息:', message);
        
        // 添加用户消息到聊天界面
        emergencyAddMessage(message, 'user');
        
        // 检查API是否可用
        if (typeof getApiKeyStatus !== 'function') {
            emergencyAddMessage('❌ API配置未加载', 'system');
            return;
        }
        
        const keyStatus = getApiKeyStatus();
        if (!keyStatus.gemini.available) {
            emergencyAddMessage('❌ Gemini API密钥未配置', 'system');
            return;
        }
        
        // 发送到后台处理
        const response = await chrome.runtime.sendMessage({
            action: 'emergency_chat',
            message: message
        });
        
        if (response && response.success) {
            emergencyAddMessage(response.reply, 'assistant');
        } else {
            emergencyAddMessage('❌ 发送失败: ' + (response?.error || '未知错误'), 'system');
        }
        
    } catch (error) {
        console.error('发送消息失败:', error);
        emergencyAddMessage('❌ 发送异常: ' + error.message, 'system');
    }
}

/**
 * @function emergencyAddMessage - 紧急添加消息到聊天界面
 * @param {string} content - 消息内容
 * @param {string} type - 消息类型 (user/assistant/system)
 */
function emergencyAddMessage(content, type) {
    const messageList = document.getElementById('aisp-message-list');
    if (!messageList) {
        console.log(`[${type}] ${content}`);
        return;
    }
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `aisp-message aisp-message-${type}`;
    
    const timestamp = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
        <div class="aisp-message-content">
            <div class="aisp-message-text">${content}</div>
            <div class="aisp-message-time">${timestamp}</div>
        </div>
    `;
    
    messageList.appendChild(messageDiv);
    messageList.scrollTop = messageList.scrollHeight;
}

/**
 * @function emergencyTestConnection - 紧急测试API连接
 */
async function emergencyTestConnection() {
    try {
        console.log('🔄 测试API连接...');
        emergencyAddMessage('🔄 正在测试API连接...', 'system');
        
        const response = await chrome.runtime.sendMessage({
            action: 'test_api_connection'
        });
        
        if (response && response.success) {
            const status = response.data;
            const message = status.connected 
                ? `✅ 连接成功！响应时间: ${status.responseTime || 'N/A'}ms`
                : `❌ 连接失败: ${status.error || '未知错误'}`;
            emergencyAddMessage(message, 'system');
            
            // 更新状态显示
            emergencyUpdateConnectionStatus(status.connected);
        } else {
            emergencyAddMessage('❌ 连接测试失败: ' + (response?.error || '未知错误'), 'system');
        }
        
    } catch (error) {
        console.error('测试连接失败:', error);
        emergencyAddMessage('❌ 连接测试异常: ' + error.message, 'system');
    }
}

/**
 * @function emergencyUpdateConnectionStatus - 紧急更新连接状态显示
 * @param {boolean} connected - 是否连接成功
 */
function emergencyUpdateConnectionStatus(connected) {
    const statusElement = document.getElementById('aisp-connection-status');
    const statusDot = statusElement?.querySelector('.aisp-status-dot');
    const statusText = statusElement?.querySelector('.aisp-status-text');
    
    if (statusDot) {
        statusDot.className = `aisp-status-dot ${connected ? 'aisp-status-connected' : 'aisp-status-disconnected'}`;
    }
    
    if (statusText) {
        statusText.textContent = connected ? '已连接' : '连接失败';
    }
}

/**
 * @function emergencyInitialize - 紧急初始化
 */
function emergencyInitialize() {
    console.log('🚀 开始紧急初始化...');
    
    // 修复按钮功能
    emergencyButtonFix();
    
    // 添加欢迎消息
    setTimeout(() => {
        emergencyAddMessage('🔧 AI侧边栏已进入紧急修复模式', 'system');
        emergencyAddMessage('基本功能已恢复，正在诊断其他问题...', 'system');
    }, 500);
    
    // 测试连接状态
    setTimeout(() => {
        emergencyTestConnection();
    }, 1000);
    
    console.log('✅ 紧急修复完成');
}

// 等待DOM加载完成后执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', emergencyInitialize);
} else {
    emergencyInitialize();
}

// 导出函数供调试使用
window.emergencyFix = {
    buttonFix: emergencyButtonFix,
    testConnection: emergencyTestConnection,
    sendMessage: emergencySendMessage,
    addMessage: emergencyAddMessage
};

console.log('🔧 紧急修复脚本已加载');
