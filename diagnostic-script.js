/**
 * @file diagnostic-script.js - AI侧边栏扩展程序诊断脚本
 * @description 全面诊断扩展程序的各种问题
 */

console.log('🔍 开始AI侧边栏扩展程序诊断...');

/**
 * @function checkJavaScriptErrors - 检查JavaScript错误
 */
function checkJavaScriptErrors() {
    console.log('\n📋 1. JavaScript错误检查');
    
    // 检查全局变量冲突
    const globalVars = [
        'aisp_isInitialized',
        'SUPPORTED_LANGUAGES',
        'aisp_currentLanguage'
    ];
    
    globalVars.forEach(varName => {
        try {
            if (window[varName] !== undefined) {
                console.log(`✅ ${varName}: 已定义`);
            } else {
                console.log(`⚠️ ${varName}: 未定义`);
            }
        } catch (error) {
            console.error(`❌ ${varName}: 检查失败 -`, error.message);
        }
    });
    
    // 检查重要函数
    const importantFunctions = [
        'aisp_initializeSidePanel',
        'aisp_setupEventListeners',
        'aisp_updateConnectionStatus',
        'aisp_capturePageContent'
    ];
    
    importantFunctions.forEach(funcName => {
        try {
            if (typeof window[funcName] === 'function') {
                console.log(`✅ ${funcName}: 函数存在`);
            } else {
                console.log(`❌ ${funcName}: 函数不存在`);
            }
        } catch (error) {
            console.error(`❌ ${funcName}: 检查失败 -`, error.message);
        }
    });
}

/**
 * @function checkSidepanelButtons - 检查侧边栏按钮
 */
function checkSidepanelButtons() {
    console.log('\n🔘 2. 侧边栏按钮检查');
    
    const buttonSelectors = [
        '#aisp-analyze-btn',
        '#aisp-template-btn',
        '#aisp-settings-btn',
        '#aisp-send-btn',
        '.aisp-action-button'
    ];
    
    buttonSelectors.forEach(selector => {
        const button = document.querySelector(selector);
        if (button) {
            console.log(`✅ ${selector}: 元素存在`);
            
            // 检查事件监听器
            const events = getEventListeners ? getEventListeners(button) : {};
            if (Object.keys(events).length > 0) {
                console.log(`  📎 事件监听器:`, Object.keys(events));
            } else {
                console.log(`  ⚠️ 无事件监听器`);
            }
            
            // 检查CSS样式
            const style = window.getComputedStyle(button);
            if (style.pointerEvents === 'none') {
                console.log(`  ❌ CSS阻止点击: pointer-events: none`);
            }
            if (style.display === 'none') {
                console.log(`  ❌ 元素隐藏: display: none`);
            }
        } else {
            console.log(`❌ ${selector}: 元素不存在`);
        }
    });
}

/**
 * @function checkContentScript - 检查内容脚本
 */
function checkContentScript() {
    console.log('\n📄 3. 内容脚本检查');
    
    // 检查content script是否注入
    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
        if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'ping'}, (response) => {
                if (chrome.runtime.lastError) {
                    console.log('❌ Content Script未注入:', chrome.runtime.lastError.message);
                } else {
                    console.log('✅ Content Script已注入');
                    
                    // 检查自动抓取功能
                    chrome.tabs.sendMessage(tabs[0].id, {action: 'check_auto_capture'}, (response) => {
                        if (response && response.enabled) {
                            console.log('✅ 自动页面抓取功能已启用');
                        } else {
                            console.log('❌ 自动页面抓取功能未启用');
                        }
                    });
                }
            });
        }
    });
}

/**
 * @function checkAPIConnection - 检查API连接
 */
function checkAPIConnection() {
    console.log('\n🌐 4. API连接检查');
    
    // 检查API密钥配置
    if (typeof getApiKeyStatus === 'function') {
        const keyStatus = getApiKeyStatus();
        console.log('API密钥状态:', keyStatus);
        
        if (keyStatus.gemini.available) {
            console.log('✅ Gemini API密钥已配置');
            
            // 测试API连接
            chrome.runtime.sendMessage({action: 'test_api_connection'}, (response) => {
                if (response && response.success) {
                    console.log('✅ API连接测试成功');
                } else {
                    console.log('❌ API连接测试失败:', response?.error);
                }
            });
        } else {
            console.log('❌ Gemini API密钥未配置');
        }
    } else {
        console.log('❌ getApiKeyStatus函数不存在');
    }
}

/**
 * @function checkStorageAndConfig - 检查存储和配置
 */
function checkStorageAndConfig() {
    console.log('\n💾 5. 存储和配置检查');
    
    chrome.storage.local.get(null, (items) => {
        console.log('本地存储内容:', items);
        
        if (items.aisp_config) {
            console.log('✅ 扩展配置存在');
        } else {
            console.log('⚠️ 扩展配置不存在');
        }
        
        if (items.latest_content) {
            console.log('✅ 最新页面内容存在');
        } else {
            console.log('⚠️ 最新页面内容不存在');
        }
    });
}

/**
 * @function runFullDiagnostic - 运行完整诊断
 */
function runFullDiagnostic() {
    console.log('🚀 开始完整诊断...\n');
    
    checkJavaScriptErrors();
    
    setTimeout(() => {
        checkSidepanelButtons();
    }, 500);
    
    setTimeout(() => {
        checkContentScript();
    }, 1000);
    
    setTimeout(() => {
        checkAPIConnection();
    }, 1500);
    
    setTimeout(() => {
        checkStorageAndConfig();
    }, 2000);
    
    setTimeout(() => {
        console.log('\n🎯 诊断完成！请查看上述结果。');
    }, 3000);
}

// 自动运行诊断
if (typeof chrome !== 'undefined' && chrome.runtime) {
    setTimeout(runFullDiagnostic, 1000);
} else {
    console.log('⚠️ 此脚本需要在Chrome扩展环境中运行');
}

// 导出函数供手动调用
window.runDiagnostic = runFullDiagnostic;
window.checkButtons = checkSidepanelButtons;
window.checkAPI = checkAPIConnection;

console.log('📋 诊断脚本已加载。可以手动调用 runDiagnostic() 进行诊断');
